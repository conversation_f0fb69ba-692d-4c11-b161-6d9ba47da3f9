"""
Email service for sending emails in the Task Management System
"""

import smtplib
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from flask import current_app
from app.models.api_response import ApiResponse


class EmailService:
    """Service for sending emails"""
    
    @staticmethod
    def send_email(to_email, subject, body, is_html=False):
        """
        Send an email
        
        Args:
            to_email (str): Recipient email address
            subject (str): Email subject
            body (str): Email body content
            is_html (bool): Whether the body is HTML content
            
        Returns:
            ApiResponse: Success or failure response
        """
        try:
            # Get email configuration from environment
            smtp_server = os.getenv('SMTP_SERVER')
            smtp_port = int(os.getenv('SMTP_PORT', '587'))
            smtp_username = os.getenv('SMTP_USERNAME')
            smtp_password = os.getenv('SMTP_PASSWORD')
            from_email = os.getenv('FROM_EMAIL', smtp_username)
            
            # Check if email is configured
            if not all([smtp_server, smtp_username, smtp_password]):
                return ApiResponse.failure(
                    "Email service not configured", 
                    code=500
                )
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = from_email
            msg['To'] = to_email
            
            # Add body to email
            if is_html:
                msg.attach(MIMEText(body, 'html'))
            else:
                msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()
                server.login(smtp_username, smtp_password)
                server.send_message(msg)
            
            return ApiResponse.success("Email sent successfully")
            
        except Exception as e:
            return ApiResponse.failure(
                f"Failed to send email: {str(e)}", 
                code=500
            )
    
    @staticmethod
    def send_password_reset_email(user_email, username, reset_token):
        """
        Send password reset email
        
        Args:
            user_email (str): User's email address
            username (str): User's username
            reset_token (str): Password reset token
            
        Returns:
            ApiResponse: Success or failure response
        """
        try:
            # Get base URL from environment or use default
            base_url = os.getenv('FRONTEND_URL', 'http://tms.uit.local:8084')
            reset_url = f"{base_url}/reset-password?token={reset_token}"
            
            subject = "Password Reset Request - Task Management System"
            
            # Create HTML email body
            html_body = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Password Reset</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; }}
                    .content {{ padding: 20px; background-color: #f9f9f9; }}
                    .button {{ 
                        display: inline-block; 
                        padding: 12px 24px; 
                        background-color: #007bff; 
                        color: white; 
                        text-decoration: none; 
                        border-radius: 4px; 
                        margin: 20px 0;
                    }}
                    .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Password Reset Request</h1>
                    </div>
                    <div class="content">
                        <p>Hello {username},</p>
                        <p>We received a request to reset your password for your Task Management System account.</p>
                        <p>Click the button below to reset your password:</p>
                        <p style="text-align: center;">
                            <a href="{reset_url}" class="button">Reset Password</a>
                        </p>
                        <p>Or copy and paste this link into your browser:</p>
                        <p style="word-break: break-all; background-color: #f0f0f0; padding: 10px; border-radius: 4px;">
                            {reset_url}
                        </p>
                        <p><strong>This link will expire in 30 minutes.</strong></p>
                        <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>
                    </div>
                    <div class="footer">
                        <p>This is an automated message from Task Management System. Please do not reply to this email.</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Create plain text version
            text_body = f"""
            Password Reset Request - Task Management System
            
            Hello {username},
            
            We received a request to reset your password for your Task Management System account.
            
            Please click the following link to reset your password:
            {reset_url}
            
            This link will expire in 30 minutes.
            
            If you didn't request this password reset, please ignore this email. Your password will remain unchanged.
            
            ---
            This is an automated message from Task Management System. Please do not reply to this email.
            """
            
            # Send HTML email with text fallback
            return EmailService.send_email(user_email, subject, html_body, is_html=True)
            
        except Exception as e:
            return ApiResponse.failure(
                f"Failed to send password reset email: {str(e)}", 
                code=500
            )
