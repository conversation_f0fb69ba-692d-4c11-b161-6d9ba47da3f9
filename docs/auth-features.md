# Authentication Features Documentation

## Overview

This document describes the new authentication features added to the Task Management System:

1. **Forgot Password** - Request password reset via email
2. **Reset Password** - Reset password using token from email
3. **Change Password** - Change password for authenticated users
4. **Update Password** - Alias for change password
5. **Reset Session** - Invalidate all user sessions

## API Endpoints

### 1. Forgot Password

**Endpoint:** `POST /auth/forgot-password`

**Description:** Request a password reset email

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "If an account with that email exists, a password reset link has been sent.",
  "data": null,
  "code": 200
}
```

**Notes:**
- Always returns success to prevent email enumeration attacks
- Only sends email if user exists and uses local authentication
- Reset token expires in 30 minutes

### 2. Reset Password

**Endpoint:** `POST /auth/reset-password`

**Description:** Reset password using token from email

**Request Body:**
```json
{
  "token": "reset_token_from_email",
  "new_password": "NewPassword123!"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset successfully",
  "data": null,
  "code": 200
}
```

**Notes:**
- Token is single-use and expires in 30 minutes
- Invalidates all existing JWT tokens for the user
- Password must meet strength requirements

### 3. Change Password

**Endpoint:** `POST /auth/change-password`

**Description:** Change password for authenticated users

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "current_password": "CurrentPassword123!",
  "new_password": "NewPassword123!"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password changed successfully",
  "data": null,
  "code": 200
}
```

**Notes:**
- Requires valid JWT token
- Verifies current password before changing
- New password must be different from current password

### 4. Update Password

**Endpoint:** `POST /auth/update-password`

**Description:** Alias for change password functionality

Same as change password endpoint above.

### 5. Reset Session

**Endpoint:** `POST /auth/reset-session`

**Description:** Invalidate all user sessions

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:** None

**Response:**
```json
{
  "success": true,
  "message": "All sessions have been reset successfully",
  "data": null,
  "code": 200
}
```

**Notes:**
- Invalidates all JWT tokens for the user
- User will need to login again on all devices

## Email Configuration

To enable password reset emails, configure these environment variables:

```env
# Email Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
FRONTEND_URL=http://tms.uit.local:8084
```

### Gmail Setup

For Gmail, you need to:
1. Enable 2-factor authentication
2. Generate an App Password
3. Use the App Password as `SMTP_PASSWORD`

## Security Features

### Password Requirements
- Minimum 8 characters
- Must include uppercase letter
- Must include lowercase letter
- Must include number
- Must include special character

### Token Security
- Cryptographically secure random tokens
- 30-minute expiration
- Single-use tokens
- Automatic cleanup of expired tokens

### Rate Limiting
- Consider implementing rate limiting for password reset requests
- Prevent brute force attacks on reset tokens

## Database Changes

New table `password_reset_tokens`:
- `id` - Primary key
- `user_id` - Foreign key to users table
- `token` - Unique reset token
- `expires_at` - Token expiration timestamp
- `used` - Boolean flag for single-use
- `created_at` - Creation timestamp

## Error Handling

All endpoints return standardized error responses:

```json
{
  "success": false,
  "message": "Error description",
  "data": null,
  "code": 400
}
```

Common error codes:
- `400` - Bad request (validation errors)
- `401` - Unauthorized (invalid credentials)
- `404` - User not found
- `500` - Internal server error

## Testing

Use the provided test script:

```bash
python test_auth_features.py
```

Or test manually with curl:

```bash
# Forgot password
curl -X POST http://tms.uit.local:8084/auth/forgot-password \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# Reset password
curl -X POST http://tms.uit.local:8084/auth/reset-password \
  -H "Content-Type: application/json" \
  -d '{"token": "your_token", "new_password": "NewPassword123!"}'

# Change password (requires JWT token)
curl -X POST http://tms.uit.local:8084/auth/change-password \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{"current_password": "old", "new_password": "NewPassword123!"}'
```
