#!/usr/bin/env python3
"""
Test script for new authentication features
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://tms.uit.local:8084"
API_BASE = f"{BASE_URL}/auth"

def test_forgot_password():
    """Test forgot password functionality"""
    print("🔍 Testing forgot password...")
    
    url = f"{API_BASE}/forgot-password"
    data = {
        "email": "<EMAIL>"
    }
    
    response = requests.post(url, json=data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_reset_password():
    """Test reset password functionality"""
    print("🔍 Testing reset password...")
    
    # Note: You would need a valid token from the forgot password email
    url = f"{API_BASE}/reset-password"
    data = {
        "token": "dummy_token_for_testing",
        "new_password": "NewPassword123!"
    }
    
    response = requests.post(url, json=data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_change_password():
    """Test change password functionality"""
    print("🔍 Testing change password...")
    
    # First, you need to login to get a JWT token
    login_url = f"{API_BASE}/login"
    login_data = {
        "email": "<EMAIL>",
        "password": "your_current_password"
    }
    
    # Uncomment and modify these lines with real credentials to test
    # login_response = requests.post(login_url, json=login_data)
    # if login_response.status_code == 200:
    #     token = login_response.json()["data"]["access_token"]
    #     
    #     url = f"{API_BASE}/change-password"
    #     headers = {"Authorization": f"Bearer {token}"}
    #     data = {
    #         "current_password": "your_current_password",
    #         "new_password": "NewPassword123!"
    #     }
    #     
    #     response = requests.post(url, json=data, headers=headers)
    #     print(f"Status: {response.status_code}")
    #     print(f"Response: {response.json()}")
    # else:
    #     print(f"Login failed: {login_response.json()}")
    
    print("Change password test requires valid login credentials")
    print()

def test_update_password():
    """Test update password functionality"""
    print("🔍 Testing update password...")
    print("Update password test requires valid login credentials")
    print()

def test_reset_session():
    """Test reset session functionality"""
    print("🔍 Testing reset session...")
    print("Reset session test requires valid login credentials")
    print()

def test_api_endpoints():
    """Test all new API endpoints"""
    print("=" * 50)
    print("🚀 Testing New Authentication Features")
    print("=" * 50)
    
    test_forgot_password()
    test_reset_password()
    test_change_password()
    test_update_password()
    test_reset_session()
    
    print("=" * 50)
    print("✅ Testing completed!")
    print("=" * 50)

if __name__ == "__main__":
    test_api_endpoints()
